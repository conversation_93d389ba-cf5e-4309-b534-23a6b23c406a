@echo off
chcp 65001 >nul
echo ================================================
echo 阿里云ECS密码修改脚本
echo ================================================

echo.
echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python 3.6或更高版本
    pause
    exit /b 1
)

echo.
echo 正在测试配置...
python test_config.py
if errorlevel 1 (
    echo.
    echo 配置测试失败，请检查配置后重试
    pause
    exit /b 1
)

echo.
echo 配置测试通过，开始执行密码修改...
echo.
python ecs_password_modifier.py

echo.
echo 脚本执行完成
pause
