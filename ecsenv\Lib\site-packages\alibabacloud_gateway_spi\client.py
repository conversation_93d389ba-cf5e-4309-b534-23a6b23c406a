# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
from alibabacloud_gateway_spi import models as gateway_spi_models


class Client:
    def __init__(self):
        pass

    def modify_configuration(
        self,
        context: gateway_spi_models.InterceptorContext,
        attribute_map: gateway_spi_models.AttributeMap,
    ) -> None:
        raise Exception('Un-implemented')

    async def modify_configuration_async(
        self,
        context: gateway_spi_models.InterceptorContext,
        attribute_map: gateway_spi_models.AttributeMap,
    ) -> None:
        raise Exception('Un-implemented')

    def modify_request(
        self,
        context: gateway_spi_models.InterceptorContext,
        attribute_map: gateway_spi_models.AttributeMap,
    ) -> None:
        raise Exception('Un-implemented')

    async def modify_request_async(
        self,
        context: gateway_spi_models.InterceptorContext,
        attribute_map: gateway_spi_models.AttributeMap,
    ) -> None:
        raise Exception('Un-implemented')

    def modify_response(
        self,
        context: gateway_spi_models.InterceptorContext,
        attribute_map: gateway_spi_models.AttributeMap,
    ) -> None:
        raise Exception('Un-implemented')

    async def modify_response_async(
        self,
        context: gateway_spi_models.InterceptorContext,
        attribute_map: gateway_spi_models.AttributeMap,
    ) -> None:
        raise Exception('Un-implemented')
