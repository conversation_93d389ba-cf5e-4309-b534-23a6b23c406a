# 阿里云ECS镜像创建和共享管理脚本

## 项目简介

本脚本用于自动化管理阿里云ECS镜像的创建、共享和删除操作。支持从ECS实例创建自定义镜像，并可以将镜像共享给指定的阿里云账号。

## 脚本逻辑

### 主要流程

1. **创建镜像**：调用CreateImage接口从指定ECS实例创建自定义镜像
2. **等待完成**：调用DescribeImages接口监控镜像状态，直到状态为`Available`
3. **共享镜像**：询问用户是否共享镜像，调用ModifyImageSharePermission接口共享给指定账号
4. **取消共享**：询问用户是否取消共享，调用ModifyImageSharePermission接口取消共享
5. **删除镜像**：询问用户是否删除镜像，调用DeleteImage接口删除创建的镜像

### 特殊逻辑

- 当`EXISTING_IMAGE_ID`不为None时，跳过镜像创建步骤，直接使用已有镜像进行共享操作

## 使用方法

### 1. 安装依赖
```bash
pip install alibabacloud_ecs20140526 alibabacloud_tea_openapi alibabacloud_tea_util
```

### 2. 配置参数
编辑 `ecs_image_manager.py` 文件顶部的配置参数：

```python
# 阿里云访问密钥配置
ACCESS_KEY_ID = "您的AccessKey ID"
ACCESS_KEY_SECRET = "您的AccessKey Secret"

# 实例和地域配置
REGION_ID = "cn-hangzhou"  # 杭州地域（固定）
INSTANCE_ID = "您的实例ID"  # 用于创建镜像的ECS实例ID

# 镜像配置
IMAGE_NAME = "CustomImage"  # 镜像名称
IMAGE_FAMILY = "ecs.g6"  # 镜像族系
IMAGE_VERSION = "1.0"  # 镜像版本
IMAGE_DESCRIPTION = "Custom image created from ECS instance"  # 镜像描述
IMAGE_PLATFORM = "CentOS"  # 操作系统
BOOT_MODE = "BIOS"  # 镜像的启动模式：BIOS或UEFI
ARCHITECTURE = "x86_64"  # 系统架构：x86_64或arm64

# 共享配置
SHARE_ACCOUNT_ID = "被共享的阿里云账号ID"  # 16位数字
EXISTING_IMAGE_ID = None  # 已有镜像ID，如果不为None则跳过创建镜像
```

### 3. 运行脚本
```bash
python ecs_image_manager.py
```

## 项目文件

```
├── ecs_image_manager.py      # 主脚本文件
├── img_config_example.py     # 配置文件示例
├── IMG_README.md            # 项目说明（本文件）
├── img.txt                  # 镜像信息记录文件（自动创建）
└── img_log/                 # 日志目录（自动创建）
```

## 配置要求

### 必需配置
- `ACCESS_KEY_ID`: 阿里云AccessKey ID
- `ACCESS_KEY_SECRET`: 阿里云AccessKey Secret
- `INSTANCE_ID`: 用于创建镜像的ECS实例ID
- `SHARE_ACCOUNT_ID`: 被共享的阿里云账号ID（16位数字）

### 固定配置
- `REGION_ID`: "cn-hangzhou" (杭州地域)

### 镜像配置
- `IMAGE_NAME`: 镜像名称
- `IMAGE_FAMILY`: 镜像族系
- `IMAGE_VERSION`: 镜像版本
- `IMAGE_DESCRIPTION`: 镜像描述
- `IMAGE_PLATFORM`: 操作系统
- `BOOT_MODE`: 镜像的启动模式（BIOS或UEFI）
- `ARCHITECTURE`: 系统架构（x86_64或arm64）

## 日志功能

- 所有操作都会记录详细日志
- 日志文件保存在 `img_log/` 目录下
- 文件名格式：`年-月-日-时-分-秒.log`
- 每次运行都会创建新的日志文件
- 包含API调用的详细响应信息

### 彩色日志输出

脚本支持彩色终端输出，不同级别的日志有不同的颜色：

- 🔍 **DEBUG** (蓝色) - 调试信息，API响应详情
- ✅ **INFO** (绿色) - 成功信息，正常状态更新
- ⚠️ **WARNING** (黄色) - 警告信息，需要注意
- ❌ **ERROR** (红色) - 错误信息，操作失败
- 🚨 **CRITICAL** (紫色粗体) - 严重错误，系统问题

## 镜像信息记录

- 镜像信息会自动保存到 `img.txt` 文件中
- 记录内容包括：时间戳、镜像ID、镜像名称、状态
- 每次操作都会追加记录

## 交互式操作

脚本会在关键步骤询问用户确认：
1. 是否将镜像共享给指定账号
2. 是否取消镜像共享
3. 是否删除创建的镜像

用户需要输入 `y` 或 `n` 进行确认。

## 强制要求

- 调用所有的接口都使用同一个AK
- 实例的地域为杭州(cn-hangzhou)
- AK、地域、实例ID、被共享的阿里云账号ID等配置参数均在脚本上方定义
- 创建后的镜像信息存放到img.txt内
- 调用每一个接口都要输出详细的返回内容日志便于调试排查
- 所有的日志都保存到img_log/年-月-日-时-分-秒.log内，每一次运行都产生一个新的日志文件
- 根据日志类型终端输出不同的颜色，info为绿色，error为红色，debug为蓝色

## 技术要求

- Python 3.6 或更高版本
- 网络连接（用于调用阿里云API）
- 阿里云ECS相关权限

## 阿里云API文档参考

- [CreateImage（创建自定义镜像）](https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-createimage)
- [DescribeImages（查询镜像资源）](https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-describeimages)
- [ModifyImageSharePermission（管理镜像共享权限）](https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-modifyimagesharepermission)
- [DeleteImage（删除自定义镜像）](https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-deleteimage)
