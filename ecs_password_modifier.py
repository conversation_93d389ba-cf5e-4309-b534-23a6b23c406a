#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云ECS实例密码修改脚本
根据实例状态自动选择修改策略：
1. 如果实例运行中，先停止实例再修改密码，然后启动实例
2. 如果实例已停止，直接修改密码然后启动实例
3. 如果实例启动中，提示无法修改
"""

import os
import sys
import time
import logging
from datetime import datetime
from alibabacloud_ecs20140526.client import Client as Ecs20140526Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_ecs20140526 import models as ecs_20140526_models
from alibabacloud_tea_util import models as util_models

# ==================== 配置参数加载 ====================
def load_config():
    """加载配置参数"""
    try:
        # 尝试导入config.py配置文件
        import config
        return {
            'ACCESS_KEY_ID': config.ACCESS_KEY_ID,
            'ACCESS_KEY_SECRET': config.ACCESS_KEY_SECRET,
            'REGION_ID': config.REGION_ID,
            'ZONE_ID': config.ZONE_ID,
            'INSTANCE_ID': config.INSTANCE_ID,
            'NEW_PASSWORD': config.NEW_PASSWORD,
            'LOG_DIR': getattr(config, 'LOG_DIR', 'log'),
            'POLL_INTERVAL': getattr(config, 'POLL_INTERVAL', 5),
            'MAX_WAIT_TIME': getattr(config, 'MAX_WAIT_TIME', 300)
        }
    except ImportError:
        # 如果没有config.py文件，使用默认配置
        print("警告: 未找到config.py配置文件，请复制config_example.py为config.py并填入配置信息")
        return {
            'ACCESS_KEY_ID': "your_access_key_id",
            'ACCESS_KEY_SECRET': "your_access_key_secret",
            'REGION_ID': "cn-hangzhou",
            'ZONE_ID': "cn-hangzhou-b",
            'INSTANCE_ID': "your_instance_id",
            'NEW_PASSWORD': "your_new_password",
            'LOG_DIR': "log",
            'POLL_INTERVAL': 5,
            'MAX_WAIT_TIME': 300
        }

# 加载配置
CONFIG = load_config()
ACCESS_KEY_ID = CONFIG['ACCESS_KEY_ID']
ACCESS_KEY_SECRET = CONFIG['ACCESS_KEY_SECRET']
REGION_ID = CONFIG['REGION_ID']
ZONE_ID = CONFIG['ZONE_ID']
INSTANCE_ID = CONFIG['INSTANCE_ID']
NEW_PASSWORD = CONFIG['NEW_PASSWORD']
LOG_DIR = CONFIG['LOG_DIR']
POLL_INTERVAL = CONFIG['POLL_INTERVAL']
MAX_WAIT_TIME = CONFIG['MAX_WAIT_TIME']

# 日志配置
LOG_LEVEL = logging.INFO

# ==================== 日志配置 ====================
def setup_logging():
    """设置日志配置"""
    # 创建日志目录
    if not os.path.exists(LOG_DIR):
        os.makedirs(LOG_DIR)
    
    # 生成日志文件名
    timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    log_file = os.path.join(LOG_DIR, f"{timestamp}.log")
    
    # 配置日志格式
    logging.basicConfig(
        level=LOG_LEVEL,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logging.info(f"日志文件: {log_file}")
    return log_file

# ==================== 阿里云客户端初始化 ====================
def create_client():
    """创建阿里云ECS客户端"""
    config = open_api_models.Config(
        access_key_id=ACCESS_KEY_ID,
        access_key_secret=ACCESS_KEY_SECRET
    )
    config.endpoint = f'ecs.{REGION_ID}.aliyuncs.com'
    return Ecs20140526Client(config)

# ==================== 核心功能函数 ====================
def describe_instance_status(client, instance_id):
    """查询实例状态"""
    try:
        describe_instance_status_request = ecs_20140526_models.DescribeInstanceStatusRequest(
            region_id=REGION_ID,
            instance_id=[instance_id]
        )
        runtime = util_models.RuntimeOptions()
        response = client.describe_instance_status_with_options(describe_instance_status_request, runtime)
        
        logging.info(f"查询实例状态响应: {response.body}")
        
        if response.body.instance_statuses and response.body.instance_statuses.instance_status:
            status = response.body.instance_statuses.instance_status[0].status
            logging.info(f"实例 {instance_id} 当前状态: {status}")
            return status
        else:
            logging.error(f"未找到实例 {instance_id} 的状态信息")
            return None
            
    except Exception as e:
        logging.error(f"查询实例状态失败: {str(e)}")
        raise

def stop_instance(client, instance_id):
    """停止实例"""
    try:
        stop_instance_request = ecs_20140526_models.StopInstanceRequest(
            instance_id=instance_id,
            force_stop=False
        )
        runtime = util_models.RuntimeOptions()
        response = client.stop_instance_with_options(stop_instance_request, runtime)
        
        logging.info(f"停止实例响应: {response.body}")
        logging.info(f"开始停止实例 {instance_id}")
        
    except Exception as e:
        logging.error(f"停止实例失败: {str(e)}")
        raise

def start_instance(client, instance_id):
    """启动实例"""
    try:
        start_instance_request = ecs_20140526_models.StartInstanceRequest(
            instance_id=instance_id
        )
        runtime = util_models.RuntimeOptions()
        response = client.start_instance_with_options(start_instance_request, runtime)
        
        logging.info(f"启动实例响应: {response.body}")
        logging.info(f"开始启动实例 {instance_id}")
        
    except Exception as e:
        logging.error(f"启动实例失败: {str(e)}")
        raise

def reboot_instance(client, instance_id):
    """重启实例"""
    try:
        reboot_instance_request = ecs_20140526_models.RebootInstanceRequest(
            instance_id=instance_id,
            force_stop=False
        )
        runtime = util_models.RuntimeOptions()
        response = client.reboot_instance_with_options(reboot_instance_request, runtime)
        
        logging.info(f"重启实例响应: {response.body}")
        logging.info(f"开始重启实例 {instance_id}")
        
    except Exception as e:
        logging.error(f"重启实例失败: {str(e)}")
        raise

def modify_instance_password(client, instance_id, new_password):
    """修改实例密码"""
    try:
        modify_instance_attribute_request = ecs_20140526_models.ModifyInstanceAttributeRequest(
            instance_id=instance_id,
            password=new_password
        )
        runtime = util_models.RuntimeOptions()
        response = client.modify_instance_attribute_with_options(modify_instance_attribute_request, runtime)
        
        logging.info(f"修改实例密码响应: {response.body}")
        logging.info("实例密码修改完成。")
        
    except Exception as e:
        logging.error(f"修改实例密码失败: {str(e)}")
        raise

def wait_for_status(client, instance_id, target_status, timeout=MAX_WAIT_TIME):
    """等待实例达到目标状态"""
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        current_status = describe_instance_status(client, instance_id)
        if current_status == target_status:
            logging.info(f"实例状态已变更为: {target_status}")
            return True
        
        logging.info(f"等待实例状态变更为 {target_status}，当前状态: {current_status}")
        time.sleep(POLL_INTERVAL)
    
    logging.error(f"等待实例状态变更超时，目标状态: {target_status}")
    return False

# ==================== 主要业务逻辑 ====================
def modify_password_when_stopped(client, instance_id, new_password):
    """实例停止状态下修改密码"""
    logging.info("实例已停止，直接修改密码")
    
    # 修改密码
    modify_instance_password(client, instance_id, new_password)
    
    # 启动实例
    start_instance(client, instance_id)
    
    # 等待实例启动完成
    if wait_for_status(client, instance_id, "Running"):
        logging.info("实例已启动成功。")
        return True
    else:
        logging.error("实例启动超时")
        return False

def modify_password_when_running(client, instance_id, new_password, force_stop=False):
    """实例运行状态下修改密码"""
    if force_stop:
        logging.info("实例运行中，先停止实例再修改密码")
        
        # 停止实例
        stop_instance(client, instance_id)
        
        # 等待实例停止
        if not wait_for_status(client, instance_id, "Stopped"):
            logging.error("实例停止超时")
            return False
        
        # 修改密码
        modify_instance_password(client, instance_id, new_password)
        
        # 启动实例
        start_instance(client, instance_id)
        
        # 等待实例启动完成
        if wait_for_status(client, instance_id, "Running"):
            logging.info("实例已启动成功。")
            return True
        else:
            logging.error("实例启动超时")
            return False
    else:
        logging.info("实例运行中，直接修改密码并重启")
        
        # 修改密码
        modify_instance_password(client, instance_id, new_password)
        
        # 重启实例
        reboot_instance(client, instance_id)
        
        # 等待实例重启完成
        if wait_for_status(client, instance_id, "Running"):
            logging.info("实例已启动成功。")
            return True
        else:
            logging.error("实例重启超时")
            return False

def main():
    """主函数"""
    # 设置日志
    log_file = setup_logging()
    
    logging.info("=" * 50)
    logging.info("开始执行阿里云ECS实例密码修改脚本")
    logging.info(f"实例ID: {INSTANCE_ID}")
    logging.info(f"地域: {REGION_ID}")
    logging.info(f"可用区: {ZONE_ID}")
    logging.info("=" * 50)
    
    try:
        # 创建客户端
        client = create_client()
        logging.info("阿里云ECS客户端创建成功")
        
        # 查询当前实例状态
        current_status = describe_instance_status(client, INSTANCE_ID)
        if current_status is None:
            logging.error("无法获取实例状态，脚本退出")
            return False
        
        # 根据实例状态选择处理策略
        if current_status == "Starting":
            logging.warning("实例状态为Starting(启动中)，无法修改实例信息")
            return False
        elif current_status == "Running":
            # 可以选择两种策略：停止后修改或运行中修改
            # 这里按照README要求，实现运行中修改的逻辑
            return modify_password_when_running(client, INSTANCE_ID, NEW_PASSWORD, force_stop=False)
        elif current_status == "Stopped":
            return modify_password_when_stopped(client, INSTANCE_ID, NEW_PASSWORD)
        else:
            logging.error(f"不支持的实例状态: {current_status}")
            return False
            
    except Exception as e:
        logging.error(f"脚本执行失败: {str(e)}")
        return False
    finally:
        logging.info("脚本执行结束")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
