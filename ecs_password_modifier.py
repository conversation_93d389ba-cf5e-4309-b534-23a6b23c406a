#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云ECS实例密码修改脚本
根据实例状态自动选择修改策略：
1. 如果实例运行中，先停止实例再修改密码，然后启动实例
2. 如果实例已停止，直接修改密码然后启动实例
3. 如果实例启动中，提示无法修改
"""

import os
import sys
import time
import logging
from datetime import datetime
from alibabacloud_ecs20140526.client import Client as Ecs20140526Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_ecs20140526 import models as ecs_20140526_models
from alibabacloud_tea_util import models as util_models

# ==================== 彩色日志配置 ====================
class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""

    # ANSI颜色代码和样式
    COLORS = {
        'DEBUG': '\033[36m',      # 青色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
    }

    # 特殊样式
    BOLD = '\033[1m'      # 粗体
    RESET = '\033[0m'     # 重置颜色

    # 级别图标
    ICONS = {
        'DEBUG': '🔍',
        'INFO': '✅',
        'WARNING': '⚠️',
        'ERROR': '❌',
        'CRITICAL': '🚨',
    }

    def format(self, record):
        # 获取原始格式化的消息
        log_message = super().format(record)

        # 只在终端输出时添加颜色和图标
        if hasattr(sys.stdout, 'isatty') and sys.stdout.isatty():
            level_name = record.levelname

            # 添加图标
            if level_name in self.ICONS:
                icon = self.ICONS[level_name]
                log_message = log_message.replace(f'- {level_name} -', f'- {icon} {level_name} -')

            # 添加颜色
            if level_name in self.COLORS:
                color = self.COLORS[level_name]
                # 对于ERROR和CRITICAL级别，添加粗体效果
                if level_name in ['ERROR', 'CRITICAL']:
                    log_message = f"{self.BOLD}{color}{log_message}{self.RESET}"
                else:
                    log_message = f"{color}{log_message}{self.RESET}"

        return log_message

# ==================== 配置参数加载 ====================
def load_config():
    """加载配置参数"""
    try:
        # 尝试导入config.py配置文件
        import config
        return {
            'ACCESS_KEY_ID': config.ACCESS_KEY_ID,
            'ACCESS_KEY_SECRET': config.ACCESS_KEY_SECRET,
            'REGION_ID': config.REGION_ID,
            'ZONE_ID': config.ZONE_ID,
            'INSTANCE_ID': config.INSTANCE_ID,
            'NEW_PASSWORD': config.NEW_PASSWORD,
            'LOG_DIR': getattr(config, 'LOG_DIR', 'log'),
            'POLL_INTERVAL': getattr(config, 'POLL_INTERVAL', 5),
            'MAX_WAIT_TIME': getattr(config, 'MAX_WAIT_TIME', 300)
        }
    except ImportError:
        # 如果没有config.py文件，使用默认配置
        print("警告: 未找到config.py配置文件，请复制config_example.py为config.py并填入配置信息")
        return {
            'ACCESS_KEY_ID': "your_access_key_id",
            'ACCESS_KEY_SECRET': "your_access_key_secret",
            'REGION_ID': "cn-hangzhou",
            'ZONE_ID': "cn-hangzhou-b",
            'INSTANCE_ID': "your_instance_id",
            'NEW_PASSWORD': "your_new_password",
            'LOG_DIR': "log",
            'POLL_INTERVAL': 5,
            'MAX_WAIT_TIME': 300
        }

# 加载配置
CONFIG = load_config()
ACCESS_KEY_ID = CONFIG['ACCESS_KEY_ID']
ACCESS_KEY_SECRET = CONFIG['ACCESS_KEY_SECRET'] 
REGION_ID = CONFIG['REGION_ID']
ZONE_ID = CONFIG['ZONE_ID']
INSTANCE_ID = CONFIG['INSTANCE_ID']
NEW_PASSWORD = CONFIG['NEW_PASSWORD']
LOG_DIR = CONFIG['LOG_DIR']
POLL_INTERVAL = CONFIG['POLL_INTERVAL']
MAX_WAIT_TIME = CONFIG['MAX_WAIT_TIME']

# 日志配置
LOG_LEVEL = logging.INFO

# ==================== 日志配置 ====================
def setup_logging():
    """设置日志配置"""
    # 创建日志目录
    if not os.path.exists(LOG_DIR):
        os.makedirs(LOG_DIR)

    # 生成日志文件名
    timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    log_file = os.path.join(LOG_DIR, f"{timestamp}.log")

    # 清除现有的处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # 创建文件处理器（无颜色）
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)

    # 创建控制台处理器（有颜色）
    console_handler = logging.StreamHandler(sys.stdout)
    console_formatter = ColoredFormatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)

    # 配置根日志记录器
    logging.root.setLevel(LOG_LEVEL)
    logging.root.addHandler(file_handler)
    logging.root.addHandler(console_handler)

    logging.info(f"日志文件: {log_file}")
    return log_file

# ==================== 彩色日志辅助函数 ====================
def log_success(message):
    """记录成功信息（绿色）"""
    logging.info(message)

def log_warning(message):
    """记录警告信息（黄色）"""
    logging.warning(message)

def log_error(message):
    """记录错误信息（红色）"""
    logging.error(message)

def log_debug(message):
    """记录调试信息（青色）"""
    logging.debug(message)

def log_critical(message):
    """记录严重错误信息（紫色粗体）"""
    logging.critical(message)

# ==================== 阿里云客户端初始化 ====================
def create_client():
    """创建阿里云ECS客户端"""
    config = open_api_models.Config(
        access_key_id=ACCESS_KEY_ID,
        access_key_secret=ACCESS_KEY_SECRET
    )
    config.endpoint = f'ecs.{REGION_ID}.aliyuncs.com'
    return Ecs20140526Client(config)

# ==================== 核心功能函数 ====================
def describe_instance_status(client, instance_id):
    """查询实例状态"""
    try:
        describe_instance_status_request = ecs_20140526_models.DescribeInstanceStatusRequest(
            region_id=REGION_ID,
            instance_id=[instance_id]
        )
        runtime = util_models.RuntimeOptions()
        response = client.describe_instance_status_with_options(describe_instance_status_request, runtime)
        
        log_debug(f"查询实例状态响应: {response.body}")

        if response.body.instance_statuses and response.body.instance_statuses.instance_status:
            status = response.body.instance_statuses.instance_status[0].status
            log_success(f"实例 {instance_id} 当前状态: {status}")
            return status
        else:
            log_error(f"未找到实例 {instance_id} 的状态信息")
            return None

    except Exception as e:
        log_error(f"查询实例状态失败: {str(e)}")
        raise

def stop_instance(client, instance_id):
    """停止实例"""
    try:
        stop_instance_request = ecs_20140526_models.StopInstanceRequest(
            instance_id=instance_id,
            force_stop=False
        )
        runtime = util_models.RuntimeOptions()
        response = client.stop_instance_with_options(stop_instance_request, runtime)
        
        log_debug(f"停止实例响应: {response.body}")
        log_success(f"开始停止实例 {instance_id}")

    except Exception as e:
        log_error(f"停止实例失败: {str(e)}")
        raise

def start_instance(client, instance_id):
    """启动实例"""
    try:
        start_instance_request = ecs_20140526_models.StartInstanceRequest(
            instance_id=instance_id
        )
        runtime = util_models.RuntimeOptions()
        response = client.start_instance_with_options(start_instance_request, runtime)
        
        log_debug(f"启动实例响应: {response.body}")
        log_success(f"开始启动实例 {instance_id}")

    except Exception as e:
        log_error(f"启动实例失败: {str(e)}")
        raise

def reboot_instance(client, instance_id):
    """重启实例"""
    try:
        reboot_instance_request = ecs_20140526_models.RebootInstanceRequest(
            instance_id=instance_id,
            force_stop=False
        )
        runtime = util_models.RuntimeOptions()
        response = client.reboot_instance_with_options(reboot_instance_request, runtime)
        
        log_debug(f"重启实例响应: {response.body}")
        log_success(f"开始重启实例 {instance_id}")

    except Exception as e:
        log_error(f"重启实例失败: {str(e)}")
        raise

def modify_instance_password(client, instance_id, new_password):
    """修改实例密码"""
    try:
        modify_instance_attribute_request = ecs_20140526_models.ModifyInstanceAttributeRequest(
            instance_id=instance_id,
            password=new_password
        )
        runtime = util_models.RuntimeOptions()
        response = client.modify_instance_attribute_with_options(modify_instance_attribute_request, runtime)
        
        log_debug(f"修改实例密码响应: {response.body}")
        log_success("实例密码修改完成。")

    except Exception as e:
        log_error(f"修改实例密码失败: {str(e)}")
        raise

def wait_for_status(client, instance_id, target_status, timeout=MAX_WAIT_TIME):
    """等待实例达到目标状态"""
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        current_status = describe_instance_status(client, instance_id)
        if current_status == target_status:
            log_success(f"实例状态已变更为: {target_status}")
            return True

        logging.info(f"等待实例状态变更为 {target_status}，当前状态: {current_status}")
        time.sleep(POLL_INTERVAL)

    log_error(f"等待实例状态变更超时，目标状态: {target_status}")
    return False

# ==================== 主要业务逻辑 ====================
def modify_password_when_stopped(client, instance_id, new_password):
    """实例停止状态下修改密码"""
    log_success("实例已停止，直接修改密码")

    # 修改密码
    modify_instance_password(client, instance_id, new_password)

    # 启动实例
    start_instance(client, instance_id)

    # 等待实例启动完成
    if wait_for_status(client, instance_id, "Running"):
        log_success("实例已启动成功。")
        return True
    else:
        log_error("实例启动超时")
        return False

def modify_password_when_running(client, instance_id, new_password, force_stop=False):
    """实例运行状态下修改密码"""
    if force_stop:
        log_warning("实例运行中，先停止实例再修改密码")

        # 停止实例
        stop_instance(client, instance_id)

        # 等待实例停止
        if not wait_for_status(client, instance_id, "Stopped"):
            log_error("实例停止超时")
            return False

        # 修改密码
        modify_instance_password(client, instance_id, new_password)

        # 启动实例
        start_instance(client, instance_id)

        # 等待实例启动完成
        if wait_for_status(client, instance_id, "Running"):
            log_success("实例已启动成功。")
            return True
        else:
            log_error("实例启动超时")
            return False
    else:
        log_success("实例运行中，直接修改密码并重启")

        # 修改密码
        modify_instance_password(client, instance_id, new_password)

        # 重启实例
        reboot_instance(client, instance_id)

        # 等待实例重启完成
        if wait_for_status(client, instance_id, "Running"):
            log_success("实例已启动成功。")
            return True
        else:
            log_error("实例重启超时")
            return False

def main():
    """主函数"""
    # 设置日志
    setup_logging()

    log_success("=" * 60)
    log_success("🚀 开始执行阿里云ECS实例密码修改脚本")
    logging.info(f"📋 实例ID: {INSTANCE_ID}")
    logging.info(f"🌍 地域: {REGION_ID}")
    logging.info(f"📍 可用区: {ZONE_ID}")
    log_success("=" * 60)

    try:
        # 创建客户端
        client = create_client()
        log_success("🔗 阿里云ECS客户端创建成功")

        # 查询当前实例状态
        current_status = describe_instance_status(client, INSTANCE_ID)
        if current_status is None:
            log_critical("💥 无法获取实例状态，脚本退出")
            return False

        # 根据实例状态选择处理策略
        if current_status == "Starting":
            log_warning("⏳ 实例状态为Starting(启动中)，无法修改实例信息")
            return False
        elif current_status == "Running":
            # 可以选择两种策略：停止后修改或运行中修改
            # 这里按照README要求，实现运行中修改的逻辑
            return modify_password_when_running(client, INSTANCE_ID, NEW_PASSWORD, force_stop=False)
        elif current_status == "Stopped":
            return modify_password_when_stopped(client, INSTANCE_ID, NEW_PASSWORD)
        else:
            log_error(f"❓ 不支持的实例状态: {current_status}")
            return False

    except Exception as e:
        log_critical(f"💥 脚本执行失败: {str(e)}")
        return False
    finally:
        log_success("🏁 脚本执行结束")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
