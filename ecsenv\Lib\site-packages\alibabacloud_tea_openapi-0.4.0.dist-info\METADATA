Metadata-Version: 2.4
Name: alibabacloud_tea_openapi
Version: 0.4.0
Home-page: https://github.com/
Author: Alibaba
Author-email: <EMAIL>
License: Apache License 2.0
Keywords: tea,python,tests
Platform: any
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Topic :: Software Development
Requires-Python: >=3.6
Description-Content-Type: text/markdown
Requires-Dist: darabonba-core<2.0.0,>=1.0.0
Requires-Dist: alibabacloud_credentials<2.0.0,>=1.0.2
Requires-Dist: alibabacloud_gateway_spi<1.0.0,>=0.0.2
Requires-Dist: cryptography<45.0.0,>=3.0.0
Requires-Dist: alibabacloud_tea_util<1.0.0,>=0.3.13
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: platform
Dynamic: requires-dist
Dynamic: requires-python

English | [简体中文](README-CN.md)
![](https://aliyunsdk-pages.alicdn.com/icons/AlibabaCloud.svg)

# Alibaba Cloud OpenApi SDK for Python

## Installation
- **Install with pip**

Python SDK uses a common package management tool named `pip`. If pip is not installed, see the [pip user guide](https://pip.pypa.io/en/stable/installing/ "pip User Guide") to install pip.

```bash
# Install the alibabacloud_tea_openapi
pip install alibabacloud_tea_openapi
```

## Issues

[Opening an Issue](https://github.com/aliyun/darabonba-openapi/issues/new), Issues not conforming to the guidelines may be closed immediately.

## Changelog
Detailed changes for each release are documented in the [release notes](./ChangeLog.md).

## References
* [Latest Release](https://github.com/aliyun/darabonba-openapi)

## License
[Apache-2.0](http://www.apache.org/licenses/LICENSE-2.0)

Copyright (c) 2009-present, Alibaba Cloud All rights reserved.
