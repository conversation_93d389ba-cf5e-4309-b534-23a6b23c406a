# 阿里云使用ecs创建镜像并共享脚本

脚本语言：Python

脚本主要逻辑为：

调用CreateImage接口创建一个镜像，使用实例进行创建【镜像名称、族系、版本、描述、操作系统、镜像的启动模式、系统架构等信息也再脚本中进行定义并写对应的注释】，镜像的创建不是立即完成的，需要调用

查看镜像信息DescribeImages接口查看镜像信息，当返回信息中的状态为`Available`时代表镜像创建成功并可用，然后询问是否将新镜像共享给xxx，输入y开始，输入n取消。输入y后调用ModifyImageSharePermission接口开始共享给ID为xxx的阿里云账号，共享完成后询问是否取消共享，输入y后则调用ModifyImageSharePermission接口开始取消共享ID为xxx的阿里云账号，取消成功后询问用户是否需要删除刚创建的镜像，输入y后调用DeleteImage镜像删除刚创建的镜像ID，删除完成后脚本结束。

当镜像ID为

## 强制要求：

- 调用所有的接口都使用同一个AK；
- 实例的地域为杭州(cn-hangzhou)；
- AK、地域、实例ID、实例地域、被共享的阿里云账号ID，等配置参数均放到脚本上方进行定义，便于调式时候的修改；
- 创建后的镜像信息存放到img.txt内；
- 调用每一个接口都要输出详细的返回内容日志便于调试排查；
- 所有的日志都保存到img_log/年-月-日-时-分-秒.log内，每一次运行都产生一个新的日志文件；
- 根据日志类型终端输出不同的颜色，info为绿色，error为红色，debug为蓝色；
- 只编写主要脚本和必要MD文档，禁止编写测试脚本、安装依赖等其他py、bat、sh格式的脚本。

## 阿里云接口文档：

CreateImage（创建自定义镜像）：

接口帮助文档：https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-createimage

接口OpenAPI文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/CreateImage

接口SDK示例文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/CreateImage?tab=DEMO&lang=PYTHON&RegionId=cn-hangzhou

DescribeImages（查询镜像资源）：

接口帮助文档：https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-describeimages

接口OpenAPI文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/DescribeImages?RegionId=cn-hangzhou

接口SDK示例文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/DescribeImages?RegionId=cn-hangzhou&tab=DEMO&lang=PYTHON

ModifyImageSharePermission（管理镜像共享权限）：

接口帮助文档：https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-modifyimagesharepermission

接口OpenAPI文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/ModifyImageSharePermission?tab=DOC&RegionId=cn-hangzhou

接口SDK示例文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/ModifyImageSharePermission?tab=DEMO&RegionId=cn-hangzhou&lang=PYTHON

DeleteImage（删除自定义镜像）：

接口帮助文档：https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-deleteimage

接口OpenAPI文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/DeleteImage

接口SDK示例文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/DeleteImage?tab=DEMO&lang=PYTHON&RegionId=cn-hangzhou