#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟环境运行脚本 - 在虚拟环境中运行ECS密码修改脚本
"""

import subprocess
import sys
import os
import platform

# 虚拟环境目录名
VENV_DIR = "venv"

def get_venv_python():
    """获取虚拟环境中的Python解释器路径"""
    system = platform.system().lower()
    if system == "windows":
        python_path = os.path.join(VENV_DIR, "Scripts", "python.exe")
    else:
        python_path = os.path.join(VENV_DIR, "bin", "python")
    
    if not os.path.exists(python_path):
        return None
    return python_path

def check_venv():
    """检查虚拟环境是否存在"""
    if not os.path.exists(VENV_DIR):
        print("❌ 虚拟环境不存在")
        print("请先运行 python install.py 创建虚拟环境")
        return False
    
    python_path = get_venv_python()
    if not python_path:
        print("❌ 虚拟环境Python解释器不存在")
        print("请重新运行 python install.py 创建虚拟环境")
        return False
    
    print(f"✅ 找到虚拟环境: {python_path}")
    return True

def run_test_config():
    """运行配置测试"""
    python_path = get_venv_python()
    try:
        print("正在测试配置...")
        result = subprocess.run([python_path, "test_config.py"], 
                              capture_output=True, text=True, encoding='utf-8')
        
        print(result.stdout)
        if result.stderr:
            print("错误信息:", result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 运行配置测试失败: {e}")
        return False

def run_main_script():
    """运行主脚本"""
    python_path = get_venv_python()
    try:
        print("正在运行ECS密码修改脚本...")
        print("=" * 50)
        
        # 实时输出，不捕获输出
        result = subprocess.run([python_path, "ecs_password_modifier.py"])
        
        print("=" * 50)
        if result.returncode == 0:
            print("✅ 脚本执行完成")
        else:
            print("❌ 脚本执行失败")
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 运行主脚本失败: {e}")
        return False

def show_menu():
    """显示菜单"""
    print("\n" + "=" * 50)
    print("        阿里云ECS密码修改脚本")
    print("=" * 50)
    print("请选择操作：")
    print("1. 测试配置")
    print("2. 运行密码修改脚本")
    print("3. 退出")
    print("=" * 50)

def main():
    """主函数"""
    print("阿里云ECS密码修改脚本 - 虚拟环境运行器")
    
    # 检查虚拟环境
    if not check_venv():
        return False
    
    while True:
        show_menu()
        
        try:
            choice = input("请输入选择 (1-3): ").strip()
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            return True
        
        if choice == "1":
            print("\n📋 配置测试")
            print("-" * 30)
            success = run_test_config()
            if success:
                print("\n✅ 配置测试通过！可以运行主脚本了")
            else:
                print("\n❌ 配置测试失败，请检查配置文件")
            
            input("\n按回车键继续...")
            
        elif choice == "2":
            print("\n🚀 运行密码修改脚本")
            print("-" * 30)
            
            # 先运行配置测试
            if not run_test_config():
                print("❌ 配置测试失败，无法运行主脚本")
                input("\n按回车键继续...")
                continue
            
            # 确认运行
            confirm = input("\n配置测试通过，是否继续运行密码修改脚本？(y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                run_main_script()
            else:
                print("取消运行")
            
            input("\n按回车键继续...")
            
        elif choice == "3":
            print("\n👋 再见！")
            break
            
        else:
            print("\n❌ 无效选择，请输入 1-3")
            input("按回车键继续...")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 再见！")
        sys.exit(0)
