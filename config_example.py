#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件示例
请复制此文件为 config.py 并填入真实的配置信息
"""

# ==================== 阿里云访问密钥配置 ====================
# 请在阿里云控制台创建AccessKey，并确保有ECS相关权限
ACCESS_KEY_ID = "LTAI5t..."  # 替换为您的AccessKey ID
ACCESS_KEY_SECRET = "your_access_key_secret"  # 替换为您的AccessKey Secret

# ==================== 实例配置 ====================
# 实例地域（固定为杭州）
REGION_ID = "cn-hangzhou"

# 可用区（固定为杭州B区）
ZONE_ID = "cn-hangzhou-b"

# ECS实例ID（在ECS控制台可以查看）
INSTANCE_ID = "i-bp1234567890abcdef"  # 替换为您的实例ID

# 新密码（8-30个字符，必须包含大小写字母、数字和特殊字符中的三种）
NEW_PASSWORD = "YourNewPassword123!"  # 替换为您要设置的新密码

# ==================== 其他配置 ====================
# 日志目录
LOG_DIR = "log"

# 轮询间隔（秒）
POLL_INTERVAL = 5

# 最大等待时间（秒）
MAX_WAIT_TIME = 300
