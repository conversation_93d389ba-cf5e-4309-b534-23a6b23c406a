#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云ECS镜像创建和共享管理脚本
主要功能：
1. 使用ECS实例创建自定义镜像
2. 等待镜像创建完成
3. 共享镜像给指定账号
4. 取消镜像共享
5. 删除创建的镜像
"""

import os
import sys
import time
import logging
from datetime import datetime
from alibabacloud_ecs20140526.client import Client as Ecs20140526Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_ecs20140526 import models as ecs_20140526_models
from alibabacloud_tea_util import models as util_models

# ==================== 彩色日志配置 ====================
class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[34m',      # 蓝色
        'INFO': '\033[32m',       # 绿色
        'WARNING': '\033[33m',    # 黄色
        'ERROR': '\033[31m',      # 红色
        'CRITICAL': '\033[35m',   # 紫色
    }
    
    # 特殊样式
    BOLD = '\033[1m'      # 粗体
    RESET = '\033[0m'     # 重置颜色
    
    # 级别图标
    ICONS = {
        'DEBUG': '🔍',
        'INFO': '✅',
        'WARNING': '⚠️',
        'ERROR': '❌',
        'CRITICAL': '🚨',
    }
    
    def format(self, record):
        # 获取原始格式化的消息
        log_message = super().format(record)
        
        # 只在终端输出时添加颜色和图标
        if hasattr(sys.stdout, 'isatty') and sys.stdout.isatty():
            level_name = record.levelname
            
            # 添加图标
            if level_name in self.ICONS:
                icon = self.ICONS[level_name]
                log_message = log_message.replace(f'- {level_name} -', f'- {icon} {level_name} -')
            
            # 添加颜色
            if level_name in self.COLORS:
                color = self.COLORS[level_name]
                # 对于ERROR和CRITICAL级别，添加粗体效果
                if level_name in ['ERROR', 'CRITICAL']:
                    log_message = f"{self.BOLD}{color}{log_message}{self.RESET}"
                else:
                    log_message = f"{color}{log_message}{self.RESET}"
        
        return log_message

# ==================== 配置参数 ====================
# 阿里云访问密钥配置
ACCESS_KEY_ID = "LTAI5tFhzgNi3YCGLhY3AikH"
ACCESS_KEY_SECRET = "******************************"

# 实例和地域配置
REGION_ID = "cn-hangzhou"  # 杭州地域
INSTANCE_ID = "i-bp18gdg5b3g47t0byvz0"  # 用于创建镜像的ECS实例ID

# 镜像配置
IMAGE_NAME = "zxc123"  # 镜像名称
IMAGE_FAMILY = "zxc123v1"  # 镜像族系
IMAGE_VERSION = "1.0"  # 镜像版本
IMAGE_DESCRIPTION = "Custom image created from ECS instance"  # 镜像描述
IMAGE_PLATFORM = "CentOS"  # 操作系统
BOOT_MODE = "BIOS"  # 镜像的启动模式：BIOS或UEFI
ARCHITECTURE = "x86_64"  # 系统架构：x86_64或arm64

# 共享配置
SHARE_ACCOUNT_ID = "****************"  # 被共享的阿里云账号ID
EXISTING_IMAGE_ID = "m-bp1i24gvz7o8nlo5kb9l"  # 已有镜像ID，如果不为None则跳过创建镜像

# 日志配置
LOG_DIR = "img_log"
LOG_LEVEL = logging.INFO

# 轮询配置
POLL_INTERVAL = 10  # 秒
MAX_WAIT_TIME = 1800  # 最大等待时间（秒）

# 镜像信息文件
IMAGE_INFO_FILE = "img.txt"

# ==================== 日志配置 ====================
def setup_logging():
    """设置日志配置"""
    # 创建日志目录
    if not os.path.exists(LOG_DIR):
        os.makedirs(LOG_DIR)
    
    # 生成日志文件名
    timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    log_file = os.path.join(LOG_DIR, f"{timestamp}.log")
    
    # 清除现有的处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    # 创建文件处理器（无颜色）
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    
    # 创建控制台处理器（有颜色）
    console_handler = logging.StreamHandler(sys.stdout)
    console_formatter = ColoredFormatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    
    # 配置根日志记录器
    logging.root.setLevel(LOG_LEVEL)
    logging.root.addHandler(file_handler)
    logging.root.addHandler(console_handler)
    
    logging.info(f"日志文件: {log_file}")
    return log_file

# ==================== 彩色日志辅助函数 ====================
def log_success(message):
    """记录成功信息（绿色）"""
    logging.info(message)

def log_warning(message):
    """记录警告信息（黄色）"""
    logging.warning(message)

def log_error(message):
    """记录错误信息（红色）"""
    logging.error(message)

def log_debug(message):
    """记录调试信息（蓝色）"""
    logging.debug(message)

def log_critical(message):
    """记录严重错误信息（紫色粗体）"""
    logging.critical(message)

# ==================== 阿里云客户端初始化 ====================
def create_client():
    """创建阿里云ECS客户端"""
    config = open_api_models.Config(
        access_key_id=ACCESS_KEY_ID,
        access_key_secret=ACCESS_KEY_SECRET
    )
    config.endpoint = f'ecs.{REGION_ID}.aliyuncs.com'
    return Ecs20140526Client(config)

# ==================== 镜像信息保存 ====================
def save_image_info(image_id, image_name, status="Created"):
    """保存镜像信息到文件"""
    try:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        info = f"[{timestamp}] 镜像ID: {image_id}, 镜像名称: {image_name}, 状态: {status}\n"
        
        with open(IMAGE_INFO_FILE, 'a', encoding='utf-8') as f:
            f.write(info)
        
        log_success(f"镜像信息已保存到 {IMAGE_INFO_FILE}")
        
    except Exception as e:
        log_error(f"保存镜像信息失败: {str(e)}")

# ==================== 用户交互函数 ====================
def ask_user_confirmation(question):
    """询问用户确认"""
    while True:
        answer = input(f"{question} (y/n): ").strip().lower()
        if answer in ['y', 'yes']:
            return True
        elif answer in ['n', 'no']:
            return False
        else:
            print("请输入 y 或 n")

# ==================== 核心功能函数 ====================
def create_image(client, instance_id, image_name):
    """创建自定义镜像"""
    try:
        create_image_request = ecs_20140526_models.CreateImageRequest(
            region_id=REGION_ID,
            instance_id=instance_id,
            image_name=image_name,
            image_family=IMAGE_FAMILY,
            image_version=IMAGE_VERSION,
            description=IMAGE_DESCRIPTION,
            platform=IMAGE_PLATFORM,
            boot_mode=BOOT_MODE,
            architecture=ARCHITECTURE
        )
        runtime = util_models.RuntimeOptions()
        response = client.create_image_with_options(create_image_request, runtime)
        
        log_debug(f"创建镜像响应: {response.body}")
        
        if response.body.image_id:
            image_id = response.body.image_id
            log_success(f"开始创建镜像，镜像ID: {image_id}")
            save_image_info(image_id, image_name, "Creating")
            return image_id
        else:
            log_error("创建镜像失败，未返回镜像ID")
            return None
            
    except Exception as e:
        log_error(f"创建镜像失败: {str(e)}")
        raise

def describe_images(client, image_id):
    """查询镜像信息"""
    try:
        describe_images_request = ecs_20140526_models.DescribeImagesRequest(
            region_id=REGION_ID,
            image_id=image_id
        )
        runtime = util_models.RuntimeOptions()
        response = client.describe_images_with_options(describe_images_request, runtime)
        
        log_debug(f"查询镜像信息响应: {response.body}")
        
        if response.body.images and response.body.images.image:
            image_info = response.body.images.image[0]
            status = image_info.status
            log_success(f"镜像 {image_id} 当前状态: {status}")
            return status, image_info
        else:
            log_error(f"未找到镜像 {image_id} 的信息")
            return None, None
            
    except Exception as e:
        log_error(f"查询镜像信息失败: {str(e)}")
        raise

def wait_for_image_available(client, image_id):
    """等待镜像创建完成"""
    log_success(f"等待镜像 {image_id} 创建完成...")
    start_time = time.time()
    
    while time.time() - start_time < MAX_WAIT_TIME:
        status, image_info = describe_images(client, image_id)
        if status == "Available":
            log_success(f"镜像 {image_id} 创建完成，状态: Available")
            save_image_info(image_id, image_info.image_name if image_info else IMAGE_NAME, "Available")
            return True
        elif status in ["CreateFailed", "UnAvailable"]:
            log_error(f"镜像创建失败，状态: {status}")
            return False
        
        logging.info(f"镜像状态: {status}，继续等待...")
        time.sleep(POLL_INTERVAL)
    
    log_error(f"等待镜像创建超时，最大等待时间: {MAX_WAIT_TIME}秒")
    return False

def modify_image_share_permission(client, image_id, account_id, operation):
    """管理镜像共享权限"""
    try:
        modify_image_share_request = ecs_20140526_models.ModifyImageSharePermissionRequest(
            region_id=REGION_ID,
            image_id=image_id,
            add_account=[account_id] if operation == "share" else None,
            remove_account=[account_id] if operation == "unshare" else None
        )
        runtime = util_models.RuntimeOptions()
        response = client.modify_image_share_permission_with_options(modify_image_share_request, runtime)

        log_debug(f"修改镜像共享权限响应: {response.body}")

        if operation == "share":
            log_success(f"镜像 {image_id} 已成功共享给账号 {account_id}")
            save_image_info(image_id, IMAGE_NAME, f"Shared to {account_id}")
        else:
            log_success(f"镜像 {image_id} 已取消共享给账号 {account_id}")
            save_image_info(image_id, IMAGE_NAME, f"Unshared from {account_id}")

        return True

    except Exception as e:
        log_error(f"修改镜像共享权限失败: {str(e)}")
        raise

def delete_image(client, image_id):
    """删除自定义镜像"""
    try:
        delete_image_request = ecs_20140526_models.DeleteImageRequest(
            region_id=REGION_ID,
            image_id=image_id,
            force=True
        )
        runtime = util_models.RuntimeOptions()
        response = client.delete_image_with_options(delete_image_request, runtime)

        log_debug(f"删除镜像响应: {response.body}")
        log_success(f"镜像 {image_id} 已成功删除")
        save_image_info(image_id, IMAGE_NAME, "Deleted")

        return True

    except Exception as e:
        log_error(f"删除镜像失败: {str(e)}")
        raise

# ==================== 主要业务逻辑 ====================
def process_image_workflow(client, image_id=None):
    """处理镜像工作流程"""
    created_image_id = None

    try:
        # 如果没有提供镜像ID，则创建新镜像
        if image_id is None:
            log_success("开始创建新镜像...")
            created_image_id = create_image(client, INSTANCE_ID, IMAGE_NAME)
            if not created_image_id:
                return False

            # 等待镜像创建完成
            if not wait_for_image_available(client, created_image_id):
                return False

            image_id = created_image_id
        else:
            log_success(f"使用已有镜像ID: {image_id}")
            # 验证镜像是否存在且可用
            status, _ = describe_images(client, image_id)
            if status != "Available":
                log_error(f"镜像 {image_id} 状态不可用: {status}")
                return False

        # 询问是否共享镜像
        if ask_user_confirmation(f"是否将镜像 {image_id} 共享给账号 {SHARE_ACCOUNT_ID}？"):
            modify_image_share_permission(client, image_id, SHARE_ACCOUNT_ID, "share")

            # 询问是否取消共享
            if ask_user_confirmation("是否取消共享？"):
                modify_image_share_permission(client, image_id, SHARE_ACCOUNT_ID, "unshare")

        # 如果是新创建的镜像，询问是否删除
        if created_image_id:
            if ask_user_confirmation(f"是否删除刚创建的镜像 {created_image_id}？"):
                delete_image(client, created_image_id)

        return True

    except Exception as e:
        log_critical(f"镜像工作流程执行失败: {str(e)}")
        return False

def main():
    """主函数"""
    # 设置日志
    setup_logging()

    log_success("=" * 60)
    log_success("🚀 开始执行阿里云ECS镜像创建和共享管理脚本")
    logging.info(f"📋 实例ID: {INSTANCE_ID}")
    logging.info(f"🌍 地域: {REGION_ID}")
    logging.info(f"🖼️ 镜像名称: {IMAGE_NAME}")
    logging.info(f"👤 共享账号: {SHARE_ACCOUNT_ID}")
    if EXISTING_IMAGE_ID:
        logging.info(f"📷 使用已有镜像ID: {EXISTING_IMAGE_ID}")
    log_success("=" * 60)

    try:
        # 创建客户端
        client = create_client()
        log_success("🔗 阿里云ECS客户端创建成功")

        # 执行镜像工作流程
        success = process_image_workflow(client, EXISTING_IMAGE_ID)

        if success:
            log_success("🎉 脚本执行成功完成")
        else:
            log_error("💥 脚本执行失败")

        return success

    except Exception as e:
        log_critical(f"💥 脚本执行失败: {str(e)}")
        return False
    finally:
        log_success("🏁 脚本执行结束")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
