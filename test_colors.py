#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彩色日志测试脚本
"""

import logging
import sys
import os

# 添加当前目录到路径，以便导入主脚本的彩色日志类
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ecs_password_modifier import ColoredFormatter, log_success, log_warning, log_error, log_debug, log_critical

def setup_test_logging():
    """设置测试日志"""
    # 清除现有的处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    # 创建控制台处理器（有颜色）
    console_handler = logging.StreamHandler(sys.stdout)
    console_formatter = ColoredFormatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    
    # 配置根日志记录器
    logging.root.setLevel(logging.DEBUG)
    logging.root.addHandler(console_handler)

def test_colored_logs():
    """测试彩色日志输出"""
    print("=" * 60)
    print("🎨 彩色日志测试")
    print("=" * 60)
    print()
    
    setup_test_logging()
    
    print("📝 测试不同级别的日志输出：")
    print()
    
    # 测试DEBUG级别（青色）
    log_debug("这是一条调试信息 - 用于开发调试")
    
    # 测试INFO级别（绿色）
    log_success("这是一条成功信息 - 操作成功完成")
    logging.info("这是一条普通信息 - 常规状态更新")
    
    # 测试WARNING级别（黄色）
    log_warning("这是一条警告信息 - 需要注意但不影响运行")
    
    # 测试ERROR级别（红色）
    log_error("这是一条错误信息 - 操作失败")
    
    # 测试CRITICAL级别（紫色粗体）
    log_critical("这是一条严重错误信息 - 系统严重问题")
    
    print()
    print("🔍 模拟实际使用场景：")
    print()
    
    # 模拟实际使用场景
    log_success("🚀 开始执行阿里云ECS实例密码修改脚本")
    logging.info("📋 实例ID: i-bp1234567890abcdef")
    logging.info("🌍 地域: cn-hangzhou")
    logging.info("📍 可用区: cn-hangzhou-b")
    
    log_success("🔗 阿里云ECS客户端创建成功")
    log_success("实例 i-bp1234567890abcdef 当前状态: Running")
    
    log_success("实例运行中，直接修改密码并重启")
    log_success("实例密码修改完成。")
    log_success("开始重启实例 i-bp1234567890abcdef")
    
    logging.info("等待实例状态变更为 Running，当前状态: Stopping")
    logging.info("等待实例状态变更为 Running，当前状态: Starting")
    
    log_success("实例状态已变更为: Running")
    log_success("实例已启动成功。")
    log_success("🏁 脚本执行结束")
    
    print()
    print("=" * 60)
    print("✅ 彩色日志测试完成！")
    print("=" * 60)
    print()
    print("📖 颜色说明：")
    print("🔍 DEBUG   - 青色   - 调试信息")
    print("✅ INFO    - 绿色   - 成功/信息")
    print("⚠️ WARNING - 黄色   - 警告信息")
    print("❌ ERROR   - 红色   - 错误信息")
    print("🚨 CRITICAL- 紫色粗体 - 严重错误")

if __name__ == "__main__":
    test_colored_logs()
