Metadata-Version: 2.4
Name: darabonba-core
Version: 1.0.2
Summary: The darabonba module of alibabaCloud Python SDK.
Home-page: https://github.com/aliyun/tea-python
Author: <PERSON>baba Cloud
Author-email: <EMAIL>
License: Apache License 2.0
Keywords: alibabacloud,sdk,darabonba
Platform: any
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development
Requires-Python: >=3.7
Requires-Dist: alibabacloud-tea
Requires-Dist: requests<3.0.0,>=2.21.0
Requires-Dist: aiohttp<4.0.0,>=3.7.0
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: platform
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

English | [简体中文](README-CN.md)

![Alibaba Cloud](https://aliyunsdk-pages.alicdn.com/icons/AlibabaCloud.svg)

## Alibaba Cloud Tea for Python

[![PyPI version](https://badge.fury.io/py/darabonba-core.svg)](https://badge.fury.io/py/darabonba-core)
[![Python Test](https://github.com/aliyun/tea-python/actions/workflows/ci.yml/badge.svg)](https://github.com/aliyun/tea-python/actions/workflows/ci.yml)
[![codecov](https://codecov.io/gh/aliyun/tea-python/graph/badge.svg?token=FN19OMRTVY)](https://codecov.io/gh/aliyun/tea-python)
[![python](https://img.shields.io/pypi/pyversions/darabonba-core.svg)](https://img.shields.io/pypi/pyversions/darabonba-core.svg)

## Important Updates

- Starting from version 1.0.0, the package `darabonba-core` only supports Python 3.7 and above.

## Installation

- **Install with pip**

Python SDK uses a common package management tool named `pip`. If pip is not installed, see the [pip user guide](https://pip.pypa.io/en/stable/installing/ "pip User Guide") to install pip.

```bash
# Install the darabonba-core
pip install darabonba-core
```

## Issues

[Opening an Issue](https://github.com/aliyun/tea-python/issues/new), Issues not conforming to the guidelines may be closed immediately.

## Changelog

Detailed changes for each release are documented in the [release notes](./ChangeLog.md).

## References

- [Latest Release](https://github.com/aliyun/tea-python/tree/master/python)

## License

[Apache-2.0](http://www.apache.org/licenses/LICENSE-2.0)

Copyright (c) 2009-present, Alibaba Cloud All rights reserved.
