# 阿里云修改ECS密码脚本

脚本语言：Python

脚本主要逻辑为：

调用DescribeInstanceStatus接口查看实例状态，如果是Running(运行中)则调用StopInstance接口停止实例，并持续调用DescribeInstanceStatus接口查看实例状态，当实例状态为：Stopped(已停止)时，调用ModifyInstanceAttribute接口来修改实例密码，调用成功后输出“实例密码修改完成。“然后调用StartInstance接口启动实例，并持续调用DescribeInstanceStatus接口查看实例状态，当实例状态为Running(运行中)则返回输出“实例已启动成功。”

运行中修改

调用DescribeInstanceStatus接口查看实例状态，如果是Starting(启动中)则返回“实例状态为arting(启动中)，无法修改实例信息”，如果不是则调用ModifyInstanceAttribute接口来修改实例密码，调用成功后输出“实例密码修改完成。“然后调用RebootInstance接口重启实例，并持续调用DescribeInstanceStatus接口查看实例状态，当实例状态为Running(运行中)则返回输出“实例已启动成功。”

## 强制要求：

- 调用所有的接口都使用同一个AK；
- 实例的地域为杭州(cn-hangzhou)，可用区为(cn-hangzhou-b)；
- AK、地域、实例ID、要修改的密码、实例地域、可用区、等配置参数均放到脚本上方进行定义，便于调式时候的修改
- 调用每一个接口都要输出详细的返回内容日志便于调试排查；
- 所有的日志都保存到log/年-月-日-时-分-秒.log内，每一次运行都产生一个新的日志文件；
- 

## 阿里云接口文档：

StartInstance（启动实例）：

接口帮助文档：https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-startinstance

接口OpenAPI文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/StartInstance

接口SDK示例文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/StartInstance?lang=PYTHON&tab=DEMO

StopInstance（停止实例）：

接口帮助文档：https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-stopinstance

接口OpenAPI文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/StopInstance

接口SDK示例文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/StopInstance?lang=PYTHON&tab=DEMO

RebootInstance（重启实例）：

接口帮助文档：https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-rebootinstance

接口OpenAPI文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/RebootInstance

接口SDK示例文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/RebootInstance?tab=DEMO&lang=PYTHON

DescribeInstanceStatus（查询实例的状态信息列表）：

接口帮助文档：https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-describeinstancestatus

接口OpenAPI文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/DescribeInstanceStatus

接口SDK示例文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/DescribeInstanceTypes?tab=DEMO&lang=PYTHON

ModifyInstanceAttribute（修改实例属性信息）：

接口帮助文档：https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-modifyinstanceattribute

接口OpenAPI文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/ModifyInstanceAttribute

接口SDK示例文档：https://next.api.aliyun.com/api/Ecs/2014-05-26/ModifyInstanceAttribute?tab=DEMO&lang=PYTHON

