#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
镜像管理脚本配置示例
请直接修改 ecs_image_manager.py 文件顶部的配置参数
"""

# 阿里云访问密钥配置
ACCESS_KEY_ID = "LTAI5t..."  # 替换为您的AccessKey ID
ACCESS_KEY_SECRET = "your_access_key_secret"  # 替换为您的AccessKey Secret

# 实例和地域配置
REGION_ID = "cn-hangzhou"  # 杭州地域（固定）
INSTANCE_ID = "i-bp1234567890abcdef"  # 用于创建镜像的ECS实例ID

# 镜像配置
IMAGE_NAME = "CustomImage"  # 镜像名称
IMAGE_FAMILY = "ecs.g6"  # 镜像族系
IMAGE_VERSION = "1.0"  # 镜像版本
IMAGE_DESCRIPTION = "Custom image created from ECS instance"  # 镜像描述
IMAGE_PLATFORM = "CentOS"  # 操作系统
BOOT_MODE = "BIOS"  # 镜像的启动模式：BIOS或UEFI
ARCHITECTURE = "x86_64"  # 系统架构：x86_64或arm64

# 共享配置
SHARE_ACCOUNT_ID = "****************"  # 被共享的阿里云账号ID
EXISTING_IMAGE_ID = None  # 已有镜像ID，如果不为None则跳过创建镜像

# 注意：
# 1. 请确保AccessKey有ECS相关权限
# 2. 实例ID必须在杭州地域
# 3. 共享账号ID为16位数字
# 4. 如果设置了EXISTING_IMAGE_ID，将跳过镜像创建步骤
