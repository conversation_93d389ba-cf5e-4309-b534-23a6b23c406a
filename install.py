#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装脚本 - 自动安装依赖包
"""

import subprocess
import sys
import os

def install_requirements():
    """安装依赖包"""
    try:
        print("正在安装阿里云ECS SDK依赖包...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖包安装完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装依赖包失败: {e}")
        return False

def create_config_file():
    """创建配置文件"""
    if not os.path.exists("config.py"):
        try:
            import shutil
            shutil.copy("config_example.py", "config.py")
            print("已创建config.py配置文件，请编辑此文件填入您的配置信息")
        except Exception as e:
            print(f"创建配置文件失败: {e}")
            return False
    else:
        print("config.py配置文件已存在")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("阿里云ECS密码修改脚本安装程序")
    print("=" * 50)
    
    # 安装依赖
    if not install_requirements():
        print("安装失败，请检查网络连接和Python环境")
        return False
    
    # 创建配置文件
    if not create_config_file():
        print("配置文件创建失败")
        return False
    
    print("\n安装完成！")
    print("\n使用步骤：")
    print("1. 编辑 config.py 文件，填入您的阿里云AccessKey和实例信息")
    print("2. 运行 python ecs_password_modifier.py 执行密码修改")
    print("\n注意事项：")
    print("- 请确保AccessKey有ECS相关权限")
    print("- 新密码必须符合阿里云密码复杂度要求")
    print("- 脚本会自动创建log目录保存日志文件")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
