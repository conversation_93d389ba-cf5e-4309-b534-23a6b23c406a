2025-07-27 19:26:11,344 - INFO - 日志文件: log\2025-07-27-19-26-11.log
2025-07-27 19:26:11,344 - INFO - ==================================================
2025-07-27 19:26:11,346 - INFO - 开始执行阿里云ECS实例密码修改脚本
2025-07-27 19:26:11,347 - INFO - 实例ID: 
2025-07-27 19:26:11,347 - INFO - 地域: cn-hangzhou
2025-07-27 19:26:11,350 - INFO - 可用区: cn-hangzhou-b
2025-07-27 19:26:11,350 - INFO - ==================================================
2025-07-27 19:26:11,351 - INFO - 阿里云ECS客户端创建成功
2025-07-27 19:26:11,819 - ERROR - 查询实例状态失败: Error: InvalidAccessKeyId.NotFound code: 404, Specified access key is not found. request id: 000D3B55-4285-50F9-9634-1CE6A977E80A Response: {'RequestId': '000D3B55-4285-50F9-9634-1CE6A977E80A', 'Message': 'Specified access key is not found.', 'Recommend': 'https://api.aliyun.com/troubleshoot?q=InvalidAccessKeyId.NotFound&product=Ecs&requestId=000D3B55-4285-50F9-9634-1CE6A977E80A', 'HostId': 'ecs.cn-hangzhou.aliyuncs.com', 'Code': 'InvalidAccessKeyId.NotFound', 'statusCode': 404}
2025-07-27 19:26:11,819 - ERROR - 脚本执行失败: Error: InvalidAccessKeyId.NotFound code: 404, Specified access key is not found. request id: 000D3B55-4285-50F9-9634-1CE6A977E80A Response: {'RequestId': '000D3B55-4285-50F9-9634-1CE6A977E80A', 'Message': 'Specified access key is not found.', 'Recommend': 'https://api.aliyun.com/troubleshoot?q=InvalidAccessKeyId.NotFound&product=Ecs&requestId=000D3B55-4285-50F9-9634-1CE6A977E80A', 'HostId': 'ecs.cn-hangzhou.aliyuncs.com', 'Code': 'InvalidAccessKeyId.NotFound', 'statusCode': 404}
2025-07-27 19:26:11,821 - INFO - 脚本执行结束
2025-07-27 19:26:11,822 - DEBUG - Shutting down executor...
