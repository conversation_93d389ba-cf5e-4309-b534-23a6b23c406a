#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置测试脚本 - 验证配置是否正确
"""

import os
import sys
import re

def test_config():
    """测试配置文件"""
    print("正在测试配置文件...")
    
    try:
        import config
    except ImportError:
        print("❌ 错误: 未找到config.py文件")
        print("请复制config_example.py为config.py并填入配置信息")
        return False
    
    # 检查必需的配置项
    required_configs = [
        'ACCESS_KEY_ID',
        'ACCESS_KEY_SECRET', 
        'REGION_ID',
        'ZONE_ID',
        'INSTANCE_ID',
        'NEW_PASSWORD'
    ]
    
    missing_configs = []
    for config_name in required_configs:
        if not hasattr(config, config_name):
            missing_configs.append(config_name)
        else:
            value = getattr(config, config_name)
            if not value or value.startswith('your_'):
                missing_configs.append(config_name)
    
    if missing_configs:
        print("❌ 错误: 以下配置项未正确设置:")
        for config_name in missing_configs:
            print(f"  - {config_name}")
        return False
    
    # 验证配置格式
    errors = []
    
    # 验证AccessKey ID格式
    if not re.match(r'^LTAI[a-zA-Z0-9]{12,20}$', config.ACCESS_KEY_ID):
        errors.append("ACCESS_KEY_ID格式不正确，应该以LTAI开头")
    
    # 验证地域
    if config.REGION_ID != "cn-hangzhou":
        errors.append("REGION_ID必须为cn-hangzhou")
    
    # 验证可用区
    if config.ZONE_ID != "cn-hangzhou-b":
        errors.append("ZONE_ID必须为cn-hangzhou-b")
    
    # 验证实例ID格式
    if not re.match(r'^i-[a-zA-Z0-9]{8,25}$', config.INSTANCE_ID):
        errors.append("INSTANCE_ID格式不正确，应该以i-开头")
    
    # 验证密码复杂度
    password = config.NEW_PASSWORD
    if len(password) < 8 or len(password) > 30:
        errors.append("密码长度必须在8-30个字符之间")
    
    # 检查密码复杂度（至少包含大小写字母、数字、特殊字符中的三种）
    has_upper = bool(re.search(r'[A-Z]', password))
    has_lower = bool(re.search(r'[a-z]', password))
    has_digit = bool(re.search(r'[0-9]', password))
    has_special = bool(re.search(r'[()~!@#$%^&*\-+=|{}[\]:;\'<>,.?/]', password))
    
    complexity_count = sum([has_upper, has_lower, has_digit, has_special])
    if complexity_count < 3:
        errors.append("密码必须包含大小写字母、数字和特殊字符中的至少三种")
    
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    print("✅ 配置验证通过!")
    print(f"  - 实例ID: {config.INSTANCE_ID}")
    print(f"  - 地域: {config.REGION_ID}")
    print(f"  - 可用区: {config.ZONE_ID}")
    print(f"  - AccessKey ID: {config.ACCESS_KEY_ID[:8]}...")
    
    return True

def test_dependencies():
    """测试依赖包"""
    print("\n正在测试依赖包...")
    
    required_packages = [
        'alibabacloud_ecs20140526',
        'alibabacloud_tea_openapi',
        'alibabacloud_tea_util'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print("\n❌ 缺少依赖包，请运行以下命令安装:")
        print("python install.py")
        print("或者:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装!")
    return True

def test_log_directory():
    """测试日志目录"""
    print("\n正在测试日志目录...")
    
    log_dir = "log"
    if not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir)
            print(f"✅ 创建日志目录: {log_dir}")
        except Exception as e:
            print(f"❌ 创建日志目录失败: {e}")
            return False
    else:
        print(f"✅ 日志目录已存在: {log_dir}")
    
    # 测试写入权限
    test_file = os.path.join(log_dir, "test.log")
    try:
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print("✅ 日志目录写入权限正常")
    except Exception as e:
        print(f"❌ 日志目录写入权限异常: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("阿里云ECS密码修改脚本配置测试")
    print("=" * 50)
    
    all_passed = True
    
    # 测试配置文件
    if not test_config():
        all_passed = False
    
    # 测试依赖包
    if not test_dependencies():
        all_passed = False
    
    # 测试日志目录
    if not test_log_directory():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✅ 所有测试通过！可以运行主脚本了")
        print("运行命令: python ecs_password_modifier.py")
    else:
        print("❌ 测试失败，请根据上述提示修复问题")
    print("=" * 50)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
