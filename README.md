# 阿里云ECS密码修改脚本

## 项目简介

本项目是一个用于自动修改阿里云ECS实例登录密码的Python脚本。脚本会根据实例当前状态自动选择最适合的修改策略，确保密码修改过程的安全性和可靠性。

## 功能特性

- ✅ 自动检测实例状态并选择合适的修改策略
- ✅ 支持运行中实例的密码修改（重启方式）
- ✅ 支持停止状态实例的密码修改
- ✅ 详细的日志记录，便于调试和审计
- ✅ 配置文件管理，便于参数调整
- ✅ 完整的错误处理和状态检查
- ✅ 自动安装脚本和配置验证

## 脚本逻辑

### 主要流程

1. **检查实例状态**：调用DescribeInstanceStatus接口查看实例当前状态
2. **根据状态选择策略**：
   - 如果是`Starting`(启动中)：提示无法修改并退出
   - 如果是`Running`(运行中)：执行运行中修改流程
   - 如果是`Stopped`(已停止)：执行停止状态修改流程

### 运行中修改流程

1. 调用ModifyInstanceAttribute接口修改实例密码
2. 输出"实例密码修改完成。"
3. 调用RebootInstance接口重启实例
4. 持续调用DescribeInstanceStatus接口监控状态
5. 当实例状态为Running时输出"实例已启动成功。"

### 停止状态修改流程

1. 调用ModifyInstanceAttribute接口修改实例密码
2. 输出"实例密码修改完成。"
3. 调用StartInstance接口启动实例
4. 持续调用DescribeInstanceStatus接口监控状态
5. 当实例状态为Running时输出"实例已启动成功。"

## 快速开始

### 1. 安装依赖
```bash
python install.py
```

### 2. 配置参数
编辑 `config.py` 文件，填入您的配置信息：
```python
ACCESS_KEY_ID = "您的AccessKey ID"
ACCESS_KEY_SECRET = "您的AccessKey Secret"
INSTANCE_ID = "您的实例ID"
NEW_PASSWORD = "新密码"
```

### 3. 测试配置
```bash
python test_config.py
```

### 4. 运行脚本
```bash
python ecs_password_modifier.py
```

或者在Windows上直接双击 `run.bat`

## 项目文件结构

```
├── ecs_password_modifier.py    # 主脚本文件
├── config_example.py          # 配置文件示例
├── requirements.txt           # Python依赖包列表
├── install.py                # 自动安装脚本
├── test_config.py            # 配置测试脚本
├── run.bat                   # Windows批处理文件
├── 使用说明.md               # 详细使用说明
├── README.md                 # 项目说明（本文件）
└── log/                      # 日志目录（自动创建）
```

## 配置要求

### 必需配置
- `ACCESS_KEY_ID`: 阿里云AccessKey ID
- `ACCESS_KEY_SECRET`: 阿里云AccessKey Secret
- `INSTANCE_ID`: ECS实例ID
- `NEW_PASSWORD`: 新密码

### 固定配置
- `REGION_ID`: "cn-hangzhou" (杭州地域)
- `ZONE_ID`: "cn-hangzhou-b" (杭州B可用区)

## 密码要求

新密码必须满足以下要求：
- 长度为8-30个字符
- 必须包含大小写字母、数字和特殊字符中的三种
- 特殊字符包括：`()`~!@#$%^&*-+=|{}[]:;'<>,.?/`

## 日志功能

- 所有操作都会记录详细日志
- 日志文件保存在 `log/` 目录下
- 文件名格式：`年-月-日-时-分-秒.log`
- 每次运行都会创建新的日志文件
- 包含API调用的详细响应信息

## 安全建议

1. **保护AccessKey**
   - 不要将AccessKey提交到代码仓库
   - 定期轮换AccessKey
   - 使用最小权限原则

2. **密码安全**
   - 使用强密码
   - 定期更换密码
   - 不要在日志中记录密码明文

## 故障排除

### 常见错误

1. **权限错误 (Forbidden.RAM)**
   - 检查AccessKey是否有ECS相关权限

2. **实例不存在 (InvalidInstanceId.NotFound)**
   - 检查实例ID是否正确
   - 确认实例在指定地域

3. **密码格式错误 (InvalidPassword.Malformed)**
   - 检查新密码是否符合复杂度要求

4. **网络连接错误 (RequestTimeout)**
   - 检查网络连接，重试执行

## 技术要求

- Python 3.6 或更高版本
- 网络连接（用于安装依赖和调用API）
- 阿里云ECS相关权限

## 阿里云API文档参考

- [StartInstance（启动实例）](https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-startinstance)
- [StopInstance（停止实例）](https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-stopinstance)
- [RebootInstance（重启实例）](https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-rebootinstance)
- [DescribeInstanceStatus（查询实例状态）](https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-describeinstancestatus)
- [ModifyInstanceAttribute（修改实例属性）](https://help.aliyun.com/zh/ecs/developer-reference/api-ecs-2014-05-26-modifyinstanceattribute)
