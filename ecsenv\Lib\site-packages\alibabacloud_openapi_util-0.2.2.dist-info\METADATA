Metadata-Version: 2.4
Name: alibabacloud_openapi_util
Version: 0.2.2
Summary: Aliyun Tea OpenApi Library for Python
Home-page: https://github.com/aliyun/darabonba-openapi-util
Author: Alibaba Cloud
Author-email: <EMAIL>
License: Apache License 2.0
Keywords: alibabacloud_openapi_util
Platform: any
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Topic :: Software Development
Requires-Dist: alibabacloud_tea_util>=0.0.2
Requires-Dist: cryptography>=3.0.0
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: platform
Dynamic: requires-dist
Dynamic: summary

English | [简体中文](README-CN.md)
![](https://aliyunsdk-pages.alicdn.com/icons/AlibabaCloud.svg)

## Alibaba Cloud OpenApi Util Client for Python

## Installation
- **Install with pip**

Python SDK uses a common package management tool named `pip`. If pip is not installed, see the [pip user guide](https://pip.pypa.io/en/stable/installing/ "pip User Guide") to install pip.

```bash
# Install the alibabacloud_openapi_util
pip install alibabacloud_openapi_util
```

## Issues
[Opening an Issue](https://github.com/aliyun/darabonba-openapi-util/issues/new), Issues not conforming to the guidelines may be closed immediately.

## Changelog
Detailed changes for each release are documented in the [release notes](./ChangeLog.md).

## References
* [Latest Release](https://github.com/aliyun/darabonba-openapi-util)

## License
[Apache-2.0](http://www.apache.org/licenses/LICENSE-2.0)

Copyright (c) 2009-present, Alibaba Cloud All rights reserved.
